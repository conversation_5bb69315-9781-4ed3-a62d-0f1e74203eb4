<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Number Formatting</title>
    <script src="https://unpkg.com/imask"></script>
</head>
<body>
    <h1>Test Number Formatting</h1>
    
    <h2>Test with formato_separador_miles = true (Argentinian format)</h2>
    <p>Expected: 181,50 (comma as decimal separator)</p>
    <input type="text" alt="moneda" value="181.50" id="test1" />
    
    <h2>Test with formato_separador_miles = false (US format)</h2>
    <p>Expected: 181.50 (dot as decimal separator, no thousands separator)</p>
    <input type="text" alt="moneda" value="181.50" id="test2" />
    
    <h2>Test with larger number (formato_separador_miles = true)</h2>
    <p>Expected: 1.234.567,89 (dot as thousands, comma as decimal)</p>
    <input type="text" alt="moneda" value="1234567.89" id="test3" />
    
    <h2>Test with larger number (formato_separador_miles = false)</h2>
    <p>Expected: 1234567.89 (no thousands separator, dot as decimal)</p>
    <input type="text" alt="moneda" value="1234567.89" id="test4" />

    <script>
        // Test 1: Argentinian format
        var formato_separador_miles = true;
        testFormatting('test1', formato_separador_miles);
        
        // Test 2: US format
        formato_separador_miles = false;
        testFormatting('test2', formato_separador_miles);
        
        // Test 3: Argentinian format with large number
        formato_separador_miles = true;
        testFormatting('test3', formato_separador_miles);
        
        // Test 4: US format with large number
        formato_separador_miles = false;
        testFormatting('test4', formato_separador_miles);
        
        function testFormatting(elementId, useArgentinianFormat) {
            var element = document.getElementById(elementId);
            
            var maskConfig;
            if (useArgentinianFormat) {
                maskConfig = {
                    mask: Number,
                    scale: 2,
                    thousandsSeparator: ".",
                    padFractionalZeros: true,
                    normalizeZeros: true,
                    radix: ",",
                    mapToRadix: ["."]
                };
            } else {
                maskConfig = {
                    mask: Number,
                    scale: 2,
                    thousandsSeparator: "",
                    padFractionalZeros: true,
                    normalizeZeros: true,
                    radix: ".",
                    mapToRadix: []
                };
            }
            var mask = IMask(element, maskConfig);

            // Format initial value from backend if present
            if (element.value && /^\d+(\.\d{2})?$/.test(element.value)) {
                var num = parseFloat(element.value);
                if (!isNaN(num)) {
                    if (useArgentinianFormat) {
                        element.value = num.toLocaleString('es-AR', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                    } else {
                        element.value = num.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2, useGrouping: false});
                    }
                    mask.updateValue();
                }
            }
        }
    </script>
</body>
</html>
