var desbloqueable = false;
var confirmarsalida = false;
//var set_control_sesion = setTimeout("control_sesion()", 10000);

function bloquear() {
  var top = $(window).height() / 2 - $("#actualizando").outerHeight();
  $("#actualizando").css({
    position: "fixed",
    margin: 0,
    top: (top > 0 ? top : 0) + "px",
    width: "100%"
  });
  $("#bloquea").fadeIn(500);
  $("#actualizando").fadeIn(500);
}

function desbloquear() {
  $("#bloquea").fadeOut(500);
  $("#actualizando").fadeOut(500);
  $(".informacion_especifica").remove();
  $("#marco_flotante").fadeOut(500);
  $("#marco_flotante").html("");
  window.desbloqueable = false;
}

function marco_flotante() {
  $("#bloquea").fadeIn(500);
  $("#marco_flotante").fadeIn(500);
  window.desbloqueable = true;
}

function marco_modal() {
  $("#bloquea").fadeIn(500);
  $("#marco_flotante").fadeIn(500);
  window.desbloqueable = false;
}

function confirma(mensaje) {
  window.desbloqueable = true;
  if (confirm(mensaje)) {
    window.confirmarsalida = false;
    bloquear();
    return true;
  } else {
    // Primero devuelvo el return porque sino no se puede desbloquear
    return false;
    window.desbloqueable = false;
  }
}

function alerta(mensaje) {
  alert(mensaje);
}

function cerrar_mensaje_flotante(idmensaje, destacado = false) {
  $("#"+idmensaje).fadeOut(200);

  if (destacado) {
    $.ajax(
    {
        url: "cargadores/ajax.php",
        type: "post",
        data: (
        {
            t: window.t,
            modulo: "inicio",
            ventana: "controles",
            boton: "ocultar_destacado",
            id: idmensaje
        })
    });
  }
}

function control_sesion(boton) {
  $.ajax({
    url: "cargadores/sesion.php",
    type: "get",
    jsonp: "callback",
    dataType: "jsonp",
    data: {
      boton: boton
    },
    success: function (data) {
      $("#contenedor").append(data);
    },
    error: function () {
      alerta(
        "¡CUIDADO! Se ha producido un error conectándose con el servidor para controlar su sesión"
      );
    }
  });
}

window.onbeforeunload = function() {
  var returnValue = undefined;
  if (confirmarsalida) {
      returnValue = "Los datos que haya ingresado puede que se pierdan";
  }
  // window.desbloqueable = false;
  // eventObject.returnValue = returnValue;
  return returnValue;

}

function salir() {
  if (confirma("¿Está seguro que desea salir del sistema?")) {
    $.ajax({
      url: URL_API_LOGIN + "/salir",
      type: "get",
      dataType: "jsonp",
      error: function (jqXHR) {
        var data = jQuery.parseJSON(jqXHR.responseText);
        var mensaje = data.mensaje
          ? data.mensaje
          : "Ocurrió un error conectándose con el servidor";
        alerta(mensaje);
      },
      success: function (data) {
        document.location = data.location;
      }
    });
  }
  return false;
}

function reiniciar() {
  $.ajax({
    url: URL_API_LOGIN + "/reingresar",
    type: "get",
    jsonp: "callback",
    dataType: "jsonp",
    error: function (jqXHR) {
      var data = jQuery.parseJSON(jqXHR.responseText);
      var mensaje = data.mensaje
        ? data.mensaje
        : "Ocurrió un error conectándose con el servidor";
      alerta(mensaje);
    },
    success: function (data) {
      document.location = data.location;
    }
  });
}

function gratis() {
  marco_flotante();
  $.ajax({
    url: "cargadores/modal.php",
    type: "post",
    data: {
      modulo: "configuraciones",
      ventana: "configuraciones_gratis"
    },
    success: function (data) {
      $("#marco_flotante").html(data);
    }
  });

  return false;
}

function isFloat(n) {
  return !isNaN(n) && n.toString().indexOf(".") != -1;
}

function redondeo(n) {
  return (!isNaN(n) && n > 0.01) || n < -0.01
    ? parseFloat(n).toFixed(2)
    : "0.00";
}

// FUNCIONES DE VENTANAS PARA MIGRAR A JS EXCLUSIVO
function modal(modulo, ventana, id, callback) {
  marco_modal();
  var ser = $("form").serialize();
  $.ajax({
    url: "cargadores/modal.php",
    type: "post",
    data: {
      modulo: modulo,
      ventana: ventana,
      id: id,
      serialize: ser
    },
    success: function (data) {
      $("#marco_flotante").html(data);
      if (callback) {
        callback();
      }
    }
  });
}

function alerta_selector(tipo, valor, selector, flotar_abajo, tiempo) {
  if (typeof flotar_abajo === "undefined" || flotar_abajo === null)
    flotar_abajo = false;
  if (typeof tiempo === "undefined" || tiempo === null) tiempo = 20000;

  var selector_limpio = selector.replace(/\W/g, "");

  // Elimino la alerta anterior
  $("#alerta_selector_" + selector_limpio).remove();

  // Si hay una nueva alerta
  if (valor) {
    var my = flotar_abajo ? "left top" : "left bottom";
    var at = flotar_abajo ? "center bottom" : "center top";

    $(selector)
      .parents(".marco, #marco_flotante")
      .append(
        '<div class="' +
          tipo +
          '_especifica" id="alerta_selector_' +
          selector_limpio +
          '">' +
          '<span class="campo_texto">' +
          valor +
          "</div>"
      );
    $("#alerta_selector_" + selector_limpio).position({
      my: my,
      at: at,
      of: selector
    });
    if (tiempo)
      setTimeout(
        '$("#alerta_selector_' + selector_limpio + '").remove()',
        5000
      );
  }
}

function ahora() {
  var d = new Date();
  var ahora =
    ("0" + d.getDate()).slice(-2) +
    "-" +
    ("0" + (d.getMonth() + 1)).slice(-2) +
    "-" +
    d.getFullYear() +
    " " +
    ("0" + d.getHours()).slice(-2) +
    ":" +
    ("0" + d.getMinutes()).slice(-2);
  return ahora;
}

$(function () {
  // Global IMask initialization for all numeric/currency/percent fields (unified config)
  if (typeof IMask !== 'undefined') {
    // Detect format from PHP session variable (injected by backend)
    document.querySelectorAll('input[alt=moneda], input[alt=monedanegativa], input[alt=porcentaje], input[alt=cantidad], input[alt=cantidadnegativa], input[alt=descuento], input[alt=porcentaje]').forEach(function(element) {
      var maskConfig;
      if (formato_separador_miles) {
        maskConfig = {
          mask: Number,
          scale: 2,
          thousandsSeparator: ".",
          padFractionalZeros: true, // Always show 2 decimals
          normalizeZeros: true,
          radix: ",",
          mapToRadix: ["."]
        };
      } else {
        maskConfig = {
          mask: Number,
          scale: 2,
          thousandsSeparator: "",
          padFractionalZeros: true,
          normalizeZeros: true,
          radix: ".",
          mapToRadix: []
        };
      }
      var mask = IMask(element, maskConfig);

      // Reverse typing effect (vanilla JS)
      element.addEventListener('keydown', function(e) {
        // Only process if key is a digit or backspace/delete
        if (
          (e.key >= '0' && e.key <= '9') ||
          e.key === 'Backspace' ||
          e.key === 'Delete'
        ) {
          e.preventDefault();
          let clean = element.value.replace(/[^0-9]/g, '');
          // If all text is selected, start from empty
          if (element.selectionStart === 0 && element.selectionEnd === element.value.length) {
            clean = '';
          }
          if (e.key >= '0' && e.key <= '9') {
            clean += e.key;
          } else if (e.key === 'Backspace') {
            clean = clean.slice(0, -1);
          } else if (e.key === 'Delete') {
            clean = '';
          }
          // Always keep at least 3 digits for cents (e.g. 001 = 0,01)
          while (clean.length < 3) clean = '0' + clean;
          let intPart = clean.slice(0, -2);
          let decPart = clean.slice(-2);
          let formatted = intPart + '.' + decPart;
          if (formato_separador_miles) {
            // For Argentinian format: replace dot with comma for decimal separator
            element.value = parseFloat(formatted).toFixed(2).replace('.', ',');
          } else {
            // For US format: keep dot as decimal separator
            element.value = parseFloat(formatted).toFixed(2);
          }
          mask.updateValue();
        }
      });

      // Format initial value from backend if present
      if (element.value && /^\d+\.\d{2}$/.test(element.value)) {
        // Only format if value is plain float (e.g., 181.50)
        var num = parseFloat(element.value);
        if (!isNaN(num)) {
          if (formato_separador_miles) {
            // For Argentinian format: replace dot with comma for decimal separator
            // 181.50 -> 181,50 (no thousands separator for numbers < 1000)
            element.value = num.toFixed(2).replace('.', ',');
          } else {
            // For US format: keep dot as decimal separator
            // 181.50 -> 181.50
            element.value = num.toFixed(2);
          }
          mask.updateValue();
        }
      }
    });
    // Tiempo mask
    document.querySelectorAll('input[alt=tiempo]').forEach(function(element) {
      IMask(element, { mask: '00:00:000' });
    });
    // CUIT mask
    document.querySelectorAll('input[alt=cuit]').forEach(function(element) {
      IMask(element, { mask: '0-00000000-00' });
    });
  }
  $("ul li:has(ul)").bind(detectMobile() ? 'touchstart' : 'hover', function (e) {
    if ($(this).is(".li_menu")) $(this).addClass("li_menu_hover");
    $(this).find("ul").show();
    $(this).find("li ul").hide();
  });
  $("ul li:has(ul), ul li ul").mouseleave(function (e) {
    if ($(this).is(".li_menu")) $(this).removeClass("li_menu_hover");
    $(this).find("ul").hide();
  });

  $("img:not(.no_title), .ordenar, .input_boton_imagen")
    .live("mouseover", function () {
      $(".informacion_especifica").remove();
      window.title = $(this).attr("title");
      $(this).removeAttr("title");
      if (title && !detectMobile()) {
        $("#flotante").append(
          '<div class="informacion_especifica"><span class="campo_texto">' +
            title +
            "</span></div>"
        );
        $(".informacion_especifica").position({
          my: "center bottom",
          at: "right top",
          of: this
        });
      }
    })
    .live("mouseleave", function () {
      $(".informacion_especifica").fadeOut(200, function () {
        $(this).remove();
      });
      $(this).attr("title", window.title);
      window.title = "";
    });
  $("a").bind(detectMobile() ? 'touchstart' : 'click', function (e) {
    if (!detectMobile()) {
      if (
        e.type == "touchstart" &&
        $(this).attr("target") != "_blank" &&
        $(this).parent().parent().attr("class") != "solapas"
      ) {
        window.location.href = $(this).attr("href");
      } else if (e.type == "touchstart" && $(this).attr("target") == "_blank") {
        $("a:contains('" + $(this).html() + "')")[0].click();
        return false;
      } else if (
        $(this).attr("href").search(".php") > 0 &&
        $(this).attr("target") != "_blank" &&
        !window.desbloqueable &&
        $(this).attr("href").search("exportar") == -1
      ) {
        bloquear();
      }
    }
  });
  $("#bloquea").live(detectMobile() ? 'touchstart' : 'click', function (e) {
    if (window.desbloqueable) {
      desbloquear();
    }
  });
  $("#cerrar").live(detectMobile() ? 'touchstart' : 'click', function (e) {
    e.preventDefault();
    desbloquear();
  });

  $(".roll").each(function () {
    rollsrc = $(this).attr("src");
    rollON = rollsrc.replace(/.png$/gi, "on.png");
    $("<img>").attr("src", rollON);
  });
  $(".roll").mouseover(function () {
    imgsrc = $(this).attr("src");
    matches = imgsrc.match(/_over/);
    if (!matches) {
      imgsrcON = imgsrc.replace(/.png$/gi, "on.png");
      $(this).attr("src", imgsrcON);
    }
  });
  $(".roll").mouseout(function () {
    $(this).attr("src", imgsrc);
  });

  $("#superbuscador").live(detectMobile() ? 'touchstart' : 'click', function (e) {
    e.preventDefault();
    marco_flotante();
    $("#marco_flotante").load("cargadores/flotante.php", {
      modulo: "inicio",
      ventana: "superbuscador"
    });
  });

  $(".redactor_entrada_textarea").live("keydown", function (e) {
    if (e.keyCode == 13 && e.ctrlKey) $(".boton").first().click();
  });

  $("#salir").live(detectMobile() ? 'touchstart' : 'click', function (e) {
    e.preventDefault();
    salir();
  });
});

window.onscroll = function () {
  var speed = 4.0;
  document.body.style.backgroundPosition =
    -window.pageXOffset + "px " + -window.pageYOffset / speed + "px";
};

$.mask.options = {
  attr: "alt",
  mask: null,
  type: "fixed",
  maxLength: -1,
  defaultValue: "",
  textAlign: true,
  selectCharsOnFocus: true,
  setSize: false,
  autoTab: false,
  fixedChars: "[(),.:/ -]",
  onInvalid: function () {},
  onValid: function () {},
  onOverflow: function () {}
};


$.mask.masks = {
  moneda: {
    mask: "99.9999999999999",
    type: "reverse"
  },
  monedanegativa: {
    mask: "99.9999999999999",
    type: "reverse",
    signal: "",
    defaultValue: "+"
  },
  descuento: { mask: "99.991", type: "reverse" },
  porcentaje: { mask: "99.9999", type: "reverse", signal: "" },
  cantidad: { mask: "99.999999", type: "reverse" },
  cantidadnegativa: {
    mask: "99.999999",
    type: "reverse",
    signal: "",
    defaultValue: "+"
  },
  tiempo: { mask: "99:99:997", type: "reverse" },
  cuit: { mask: "9-99999999-99", type: "reverse" }
};


jQuery.extend(jQuery.validator.messages, {
  required: "",
  email: ""
});

function abrirMenu() {
  let menu = document.getElementById("menu");
  if (menu.style.display === "block") {
    $(".submenu").hide();
    $(".submenu2").hide();
    $("#menu").fadeOut(100);
  } else {
    $("#menu").fadeIn(100);
  }
}

function abrirSubMenu(modulo) {
  let subMenu = document.getElementById(modulo);
  if (subMenu.style.display === "block") {
    subMenu.style.display = "none";
  } else {
    subMenu.style.display = "block";
  }
}

function detectMobile() {
  return (
    typeof window.orientation !== "undefined" ||
    navigator.userAgent.indexOf("IEMobile") !== -1
  );
}

$(document).mouseup(function(e){
  var menu = $('#menu'); //Cierro menú desplegable en mobile al hacer click en cualquier lado
  if (!menu.is(e.target)
  && menu.has(e.target).length === 0
  && detectMobile())
  {
    menu.slideUp();
  }
});

$(document).bind(detectMobile() ? 'touchstart' : 'click', function (e) {
  if ($('.opciones_flotantes:visible').length > 0 && e.target.nodeName != "IMG") {
    $(".opciones_flotantes").fadeOut(500);
  }
});

iniciarRotacion();
function iniciarRotacion() {
  if (!window.location.search.substring(1)) {
    setTimeout(function(){
      let iniciarRotacion = document.getElementById('resaltar'),
        transformInicio = "transform: scale(1.2) rotate(360deg); transition: 2s;";
      iniciarRotacion.style = transformInicio;
      iniciarRotacion.webkitTransform = transformInicio;
      iniciarRotacion.style.msTransform = transformInicio;
      finalizarRotacion();
    }, 3000);
  }
}

function finalizarRotacion() {
  setTimeout(function(){
    let finalizarRotacion = document.getElementById('resaltar'),
      transformFin = "transform: scale(1) rotate(0deg); transition: 2s;";
    finalizarRotacion.style = transformFin;
    finalizarRotacion.webkitTransform = transformFin;
    finalizarRotacion.style.msTransform = transformFin;
  }, 2000);
}

function cerrar_modal() {
  $("#bloquea").fadeOut(500);
  window.close();
}

function seleccionarLocalidades() {
  marco_modal();
  $.ajax(
  {
    url: "cargadores/modal.php",
    type: "post",
    data: (
    {
        modulo: "configuraciones",
        ventana: "cambiar_localidades"
    }),
    success: function(data) {
        $("#marco_flotante").html(data);
    }
  });
}

function copyClipboard(textToCopy) {
  navigator.clipboard.writeText(textToCopy);
  alerta('Se copio <b>' + textToCopy + '</b> al portapapeles', 'confirmacion', 2);
}

function alerta(mensaje, tipo = 'alerta', tiempo = 10000) {

  let aleatorio = Math.floor(Math.random() * 10000); // Random entero de 0 a 10000
  $("#flotante").append('<div class="' + tipo + '" id="' + aleatorio +'"><span class="campo_texto">' + mensaje + '</span></div>');

  if (tiempo)
      $("#" + aleatorio).delay(tiempo).fadeOut(3000);

  if (detectMobile())
    document.documentElement.scrollTop = 0;

}

function buscadorTodo(modulo) {
  buscadorAjax(modulo, "todo");
  return false;
}

function buscadorBuscar(modulo) {
  if ($("#" + modulo + "_input").val().length < 1) {
    $("#" + modulo + "_resultados").html("");
    return false;
  }

  buscadorAjax(modulo, "buscar");
  return false;
}

function buscadorInput(modulo, keyCode) {
  if ($("#" + modulo + "_input").val().length < 3) {
    $("#" + modulo + "_resultados").html("");
    return false;
  }

  if (keyCode == 38) { // Flecha arriba
    if ($("#" + modulo + "_"+(window.flag-1)).length) {
      window.flag=window.flag-1;
      $("#" + modulo + "_"+window.flag).addClass("linea_hover");
      $("#" + modulo + "_"+(window.flag+1)).removeClass("linea_hover");
    }

  } else if (keyCode == 40) { // Flecha abajo
    if ($("#" + modulo + "_"+(window.flag+1)).length) {
      window.flag=window.flag+1;
      $("#" + modulo + "_"+window.flag).addClass("linea_hover");
      $("#" + modulo + "_"+(window.flag-1)).removeClass("linea_hover");
    }

  } else if (keyCode == 13) { // Enter
      if (window.flag == 0) {
        buscadorAjax(modulo, "ir");

      } else {
          bloquear();
          document.location = modulo + ".php?a=ver&id="+$("#" + modulo + "_"+window.flag).find("input[name=id]").val();
      }

  } else if (jQuery.inArray(keyCode, [9, 16, 18, 19, 20, 27, 33, 34, 35, 36, 37, 39, 45, 91, 92, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 144, 145]) == -1) {

    buscadorAjaxDebounced(modulo, "buscar");
    window.flag=0;
  }

  return false;
}

function buscadorAjax(modulo, boton) {

  if (window.runningRequest && typeof undefined != typeof window.request) {
    window.request.abort();
  }
  window.runningRequest=true;

  window.request = $.ajax(
  {
    url: "cargadores/ajax.php",
    type: "post",
    data: (
    {
      t: window.t,
      modulo: modulo,
      ventana: modulo + "_buscar",
      boton: boton,
      busqueda: $("#" + modulo + "_input").val()
    }),
    before: function() {
      $("#" + modulo + "_resultados").hide();
      $("#" + modulo + "_conectando").show();
    },
    success: function(data) {
      $("#" + modulo + "_resultados").html(data);
      $("#" + modulo + "_conectando").hide();
      $("#" + modulo + "_resultados").show();
    }
  });
}

function buscadorAjaxDebounced(modulo, boton) {

  $("#" + modulo + "_resultados").hide();
  $("#" + modulo + "_conectando").show();

  if (window.runningRequest && typeof undefined != typeof window.request) {
    window.request.abort();
  }
  window.runningRequest=true;

  clearTimeout(window.debounce);
  window.debounce = setTimeout(function () {

    window.request = $.ajax(
    {
      url: "cargadores/ajax.php",
      type: "post",
      data: (
      {
        t: window.t,
        modulo: modulo,
        ventana: modulo + "_buscar",
        boton: boton,
        busqueda: $("#" + modulo + "_input").val()
      }),
      success: function(data) {
        $("#" + modulo + "_resultados").html(data);
        $("#" + modulo + "_conectando").hide();
        $("#" + modulo + "_resultados").show();
      }
    });

  }, 800);

}

window.addEventListener('pageshow', function(event) {
  if (event.persisted) {
    // La página fue restaurada desde el bfcache
    window.location.reload();
  }
});

window.addEventListener('pagehide', function(event) {
  if (event.persisted) {
    // Prevenir que la página sea almacenada en el bfcache
    event.preventDefault();
  }
});
