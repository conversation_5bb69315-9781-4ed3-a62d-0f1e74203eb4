<?php
function html_inicio()
{
    global $modulo;
    global $i18n_funciones;

    header("Cache-Control: no-cache, no-store, must-revalidate");
    header("Pragma: no-cache");
    header("Expires: 0");
    header('Content-Type: text/html; charset=utf-8');

    echo '<!DOCTYPE html>
<html lang="es">
<head> ';
if ($_SESSION['usuario_idestilo'] == 2) {
    echo '
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link type="text/css" rel="stylesheet" href="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/bootstrap.min.css" />';
}

echo '
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <title>'.ucfirst($modulo).' - '.$_SESSION['sistema_nombre'].' '.$_SESSION['version'].' '.$_SESSION['servidor_version'].' - '.$_SESSION['empresa_nombre'].'</title>
    <link type="text/css" rel="stylesheet" href="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/css_reset.css" />
    <link type="text/css" rel="stylesheet" href="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/jquery-ui-1.8.18.custom.css" />
    <link type="text/css" rel="stylesheet" href="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/jquery-ui-timepicker-addon-1.1.1.css" />
    <link type="text/css" rel="stylesheet" href="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/redactor-8.2.css" />';

    if ($_SESSION['usuario_idusuario'] != ALFA_SIN_CSS)
        echo '
    <link type="text/css" rel="stylesheet" href="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/estilo.css" />';
    if ($_SESSION['usuario_idestilo'] != 1 && $_SESSION['usuario_estilo_oscuro']) {
        echo '
    <link type="text/css" rel="stylesheet" href="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/modo-oscuro-estilo_2.css" />';
    }
    // TODO: Sacar el siguiente if cuando esté terminado el módulo de exportación
    if ($modulo == 'informes' && $_SESSION['usuario_idusuario'] != ALFA_SIN_CSS)
        echo '
    <link type="text/css" rel="stylesheet" href="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/estilo_viejo.css" />';
    if ($_SESSION['configuracion_estilo_empresa'])
        echo '
    <link type="text/css" rel="stylesheet" href="'.URL_S3.$_SESSION['configuracion_estilo_empresa'].'/estilo_empresa.css" />';


    if (ESTADO != 'desarrollo') {
        echo '
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.15.0/jquery.validate.min.js"></script>';
    } else {
        echo '
    <script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/jquery-1.8.3.min.js"></script>
    <script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/jquery.validate.min.js"></script>';
    }
    echo '
    <script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/jquery-ui-1.9.2.custom.min.js"></script>
    <script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/jquery-ui-timepicker-addon-1.1.1.js"></script>
    <script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/imask.js"></script>';
    echo '
    <script>
        var formato_separador_miles = "'.$_SESSION['control_formato_separador_miles'].'";
        var URL_API_LOGIN = "'.URL_API_LOGIN.'";
        var t = "'.$_SESSION['session_token'].'";
    </script>
    <script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/redactor-8.2.modificado.js"></script>
    <script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/redactor_es-8.2.js"></script>
    <script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/redactor_fullscreen-8.2.js"></script>
    <script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/saasargentina.js?hash='.$_SESSION['hash'].'"></script>

    <!-- Favicons -->
    <link rel="icon" href="https://www.saasargentina.com/img/ico/favicon.png" type="image/png">
    <!-- <link rel="shortcut icon" href="https://www.saasargentina.com/img/ico/favicon.ico"> -->
    <link rel="apple-touch-icon" href="https://www.saasargentina.com/img/ico/apple-touch-icon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="https://www.saasargentina.com/img/ico/apple-touch-icon-72.png">
    <link rel="apple-touch-icon" sizes="114x114" href="https://www.saasargentina.com/img/ico/apple-touch-icon-114.png">
    <link rel="apple-touch-icon" sizes="144x144" href="https://www.saasargentina.com/img/ico/apple-touch-icon-144.png">

</head>

<body';

    if (!$_SESSION['configuracion_estilo_empresa'] && $_SESSION['usuario_fondo']) {
        echo ' style="background: url('.URL_S3.$_SESSION['usuario_fondo'].'/usuario_fondo.jpg) repeat-y; background-attachment: fixed; background-size: cover;"';
    }
    echo '>

    <div id="bloquea"></div>
    <div id="actualizando"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/cargando.gif" title="'.$i18n_funciones[78].'" /></div>
    <div id="marco_flotante"></div>';

    if ($_SESSION['usuario_idestilo'] == 2) {
        echo '<div class="container-fluid">';
    } else {
        echo '<div id="contenedor">';
    }

    if ($_SESSION['usuario_idestilo'] == 1 || !$_SESSION['mobile']) {
        echo '<div id="flotante"></div>';
    }
}

function html_encabezado()
{
    global $i18n_funciones;
    global $modulo;
    global $a;

    if ($_SESSION['usuario_idestilo'] == 1 || !$_SESSION['mobile']) {
        echo '<div id="encabezado">';
        if ($_SESSION['usuario_idestilo'] == 2) {
            echo '<div class="encabezado_marca'.($_SESSION['usuario_estilo_oscuro'] ? "_dark" : "").'"></div>';
        }
    echo '
    <p class="encabezado_empresa">'.$_SESSION['empresa_nombre'].' <small>(Id '.$_SESSION['empresa_idempresa'].')</small></p>
    <p class="encabezado_usuario">'.$_SESSION['usuario_nombre'].' ('.$_SESSION['usuario_mail'].')</p>
    <p class="encabezado_version">'.$_SESSION['sistema_nombre'].' '.$_SESSION['version'].' '.$_SESSION['servidor_version'].'</p>
    <p class="encabezado_fecha">'.date("d-m-Y H:i").'</p>
    <div id="botones_encabezado">';
    if ($_SESSION['sistema_gratis']) {
        if ($_SESSION['sistema_full_probado'])
            echo '
            <a href="#" class="ad_feg_encabezado" onclick="gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/full_encabezado.png"></a>';
        else
            echo '
            <a href="#" class="ad_feg_encabezado" onclick="gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/feg_encabezado.jpg"></a>';
    }
    echo '
        <a href="ayudas.php"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/indice.png" title="'.$i18n_funciones[1].'" id="resaltar"></a>';

    $notificaciones_sin_leer = campo_sql(consulta_sql("SELECT COUNT(*) FROM mensajes WHERE tipo IN ('Notificacion', 'Consulta', 'Informacion') AND idusuario = '{$_SESSION['usuario_idusuario']}' AND visto = 0"));
    if ($notificaciones_sin_leer) {
        $title_notificaciones = $notificaciones_sin_leer == 1
            ? $i18n_funciones[307]
            : str_replace('{notificaciones_sin_leer}', $notificaciones_sin_leer, $i18n_funciones[305]);
        echo '
        <a id="icono_notificaciones" href="javascript:void(0)" onclick="modal(\'mensajes\', \'mensajes_notificaciones\', \'\', \'\'); return false;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/notificaciones_alerta.png" title="'.$title_notificaciones.'"></a>';
    }
    else
        echo '
        <a id="icono_notificaciones" href="javascript:void(0)" onclick="modal(\'mensajes\', \'mensajes_notificaciones\', \'\', \'\'); return false;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/notificaciones.png" title="'.$i18n_funciones[304].'"></a>';

    if ($_SESSION['perfil_usuarios_mod'])
        echo '
        <a href="usuarios.php?a=mod&id='.$_SESSION['usuario_idusuario'].'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/config.png" title="'.$i18n_funciones[2].'"></a>';
    if ($_SESSION['perfil_configuraciones_empresa'] || $_SESSION['perfil_configuraciones_tablas']) {
        // La empresa tiene saldo y ya se le venció la factura
        if (date("j") > 10
            && $_SESSION['saldo']
            && !$_SESSION['sistema_gratis']
            && $_SESSION['empresa_estado'] == 'activada') {
            echo '
        <a href="configuraciones.php?a=situacion"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/empresa_alerta.png" title="'.$i18n_funciones[93].$i18n_funciones[105].'"></a>';

        } else {
            echo '
        <a href="configuraciones.php?a=situacion"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/empresa.png" title="'.$i18n_funciones[93].'"></a>';
        }
    }
    echo '
        <a href="#" onclick="salir()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/salir.png" title="'.$i18n_funciones[3].'"></a>
    </div>
';
    }
}

function html_cuerpo()
{
    mensajes();
    mensajes_globales();

    echo '
<div id="cuerpo">
    ';
}

function html_cuerpo_fin()
{
    echo '
</div>
    ';
}

function html_fin($js_archivos = array(), $js_funciones = array())
{
    global $modulo;
    global $i18n_funciones;

    echo '
<div id="fin">';
    if ($_SESSION['sistema_gratis']) {
        $_SESSION['ad-feg'] = (is_numeric($_SESSION['ad-feg']) && $_SESSION['ad-feg'] > 0 && $_SESSION['ad-feg'] < 10)
            ? $_SESSION['ad-feg']+1
            : 1;
        echo '
    <a href="#" onclick="gratis()"><img class="ad_feg_fin" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/ad-feg-'.$_SESSION['ad-feg'].'.png"></a>';
    }
    echo '
    <p class="fin"><a href="#" style="float:left;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/cielo.png"></a>'.$i18n_funciones[4].'</p>
    <p class="fin fin-espacio">
        <a href="https://www.instagram.com/saasargentina" target="_blank"><img class="img_rs" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/btninstagramon.png" alt="Seguinos en nuestro Instagram" class="rrss" /></a>
        <a href="https://www.facebook.com/SaasArgentina" target="_blank"><img class="img_rs" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/btnfacebookon.png" alt="SaaS Argentina en Facebook" class="rrss" /></a>
        <a href="https://www.youtube.com/user/saasargentina?sub_confirmation=1" target="_blank"><img class="img_rs" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/btnyoutubeon.png" alt="Visita nuestro canal de YouTube" class="rrss" /></a>
        <a href="https://api.whatsapp.com/send?phone=5491137844346&text=Hola%20SaaS" target="_blank"><img class="img_rs" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/btnwaon.png" alt="Whatsapp SaaS Argentina" class="rrss" /></a>
    </p>
</div>
';
    if ($_SESSION['empresa_estado'] == 'demo')
        echo '
    <div id="botoneslaterales">
        <a href="'.URL_SITE.'" alt="Regístrese para acceder"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/registrarselateral.png" alt="SignUp" class="roll" style="width: 50px;" /></a>
    </div>';
echo '
</div>
';

nuevos();

// Cargo el archivo de JS si existe para el módulo
if (file_exists(__DIR__ . '/../estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/modulos/'.$modulo.'.js')) {
    echo '
<script type="text/javascript" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/modulos/'.$modulo.'.js?hash='.$_SESSION['hash'].'"></script>';
}
// Cargo archivos de JS particulares
foreach ($js_archivos as $js_archivo) {
    echo '
<script src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/modulos/'.$js_archivo.'.js?hash='.$_SESSION['hash'].'"></script>';
}
// Asigno funciones para ejecutar en el onload
if (count($js_funciones)) {
    echo '
<script>$(function() {';
    foreach ($js_funciones as $js_funcion) {
        echo $js_funcion.'();';
    }
    echo '});</script>';
}

echo '
</body>
</html>';
}

function listar_informes($modulo)
{
    global $i18n_funciones;

    if (!isset($_SESSION['cantidades_depositos'])) {
        $_SESSION['cantidades_depositos'] = count(obtener_depositos());
    }

    if ($_SESSION['modulo_informes'] &&
        $_SESSION['perfil_'.$modulo.'_informes'] &&
        file_exists('sistemas/sistema_'.$_SESSION['sistema_idsistema'].'_idioma_'.$_SESSION['usuario_ididioma'].'/informes/informes_'.$modulo.'.php')) {
        include('sistemas/sistema_'.$_SESSION['sistema_idsistema'].'_idioma_'.$_SESSION['usuario_ididioma'].'/informes/informes_'.$modulo.'.php');
        if (count($informes)) {

            if ($_SESSION['servidor_version'] == 'BETA')
                $url_informes = URL_INFORMES_BETA;
            else if ($_SESSION['servidor_version'] == 'ALFA')
                $url_informes = URL_INFORMES_ALFA;
            else
                $url_informes = URL_INFORMES;

            echo '
                    <li><a href="#" onclick="return false">'.$i18n_funciones['informes'].'</a>
                        <div class="submenu2"><ul>';

            foreach ($informes as $informe) {
                $href = $informe['nuevo']
                    ? ($informe['menu']
                        ? 'informes.php?a=new&id='.$informe['id']
                        : $url_informes.'/informes/mod/'.$informe['id'].'?id='.$informe['id'].'&a=mod')
                    : ($informe['menu']
                        ? 'informes.php?a=filtro&id='.$informe['id']
                        : 'informes.php?a=mod&id='.$informe['id']);

                if (isset($informe['filtros']) && count($informe['filtros'])) {
                    $href.= '?';
                    foreach ($informe['filtros'] as $key => $value) {
                        if ($modulo === 'productos' && $_SESSION['cantidades_depositos'] > MAX_LISTAS_DEPOSITOS) {
                            $informes_desactivado = true;
                            $href = '';
                        } else {
                            $informes_desactivado = false;
                            $href.= $key . '=' . $value . '&';
                        }
                    }
                }

                echo '
                        <li' . ($_SESSION['sistema_gratis'] && !$informe['gratis'] ? ' class="gratis"' : ($modulo === 'productos' && $informes_desactivado && $_SESSION['cantidades_depositos'] > MAX_LISTAS_DEPOSITOS ? ' class="gratis" title="'.$i18n_funciones[322].'" '  : '')) . '>'
                        . '<a href="'.$href.'" '
                            . (($informe['nuevo'] && !$informe['menu']) ? 'target="_blank" ' : '')
                            . ($_SESSION['sistema_gratis'] && !$informe['gratis'] ? 'onclick="return gratis()"' : ($modulo === 'productos' && $informes_desactivado && $_SESSION['cantidades_depositos'] > MAX_LISTAS_DEPOSITOS ? 'onclick="return false"' : '')) . '>'
                            . $informe['nombre'] . '</a></li>';
            }
            echo '
                        </ul></div>
                    </li>
            ';
        }
    }
    // else echo 'modulo_informes:'.$_SESSION['modulo_informes'].' / perfil_modulo_informes:'.$_SESSION['perfil_'.$modulo.'_informes'];
}

function html_menu()
{
    global $i18n_funciones;
    global $modulo;
    global $moduloxinforme;

    $li_activo = array(($modulo == 'informes' ? $moduloxinforme[recibir_variable('id', true)] : $modulo) => '_activo');

    if ($_SESSION['usuario_idestilo'] == 1 || !$_SESSION['mobile']) {
        echo '
        <div id="menu">
            <ul>
                <li class="li_menu'.$li_activo['inicio'].'"><a href="index.php"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_inicio.png"></a></li>';
        if (SAAS_ID == $_SESSION['empresa_idempresa'] && $_SESSION['perfil_saas_ver']) {
            echo '
                <li class="li_menu'.$li_activo['saas'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_saas.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['saas'].'</span>';
            if ($_SESSION['perfil_saas_empresas'])
                echo '
                        <li><a href="saas.php?a=buscar">'.$i18n_funciones[17].'</a></li>';
            if ($_SESSION['perfil_saas_tickets'])
                echo '
                        <li><a href="saas.php?a=tickets">'.$i18n_funciones[62].'</a></li>
                        <li><a href="saas.php?a=faqs">'.$i18n_funciones[170].'</a></li>';
            if ($_SESSION['perfil_saas_informes']) {
                // <li><a target="_blank" href="'.URL_SCRIPTS.'/estadisticas/uso.php">Uso de funcionalidades</a></li>
                echo '
                        <li><a href="saas.php?a=mailing">'.$i18n_funciones[166].'</a></li>
                        <li><a href="saas.php?a=constantes">'.$i18n_funciones[328].'</a></li>
                        <li><a href="#" onclick="return false">'.$i18n_funciones[94].'</a>
                        <div class="submenu2"><ul>
                            <li><a target="_blank" href="'.URL_SCRIPTS.'/estadisticas/estatico.php">Generales</a></li>
                            <li><a target="_blank" href="'.URL_SCRIPTS.'/estadisticas/feg.php">FEG</a></li>
                        </ul></div>
                        </li>
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="'.URL_SCRIPTS.'/?script=descargar_log&log=afip_caido">Mensajes de ARCA</a></li>
                            <li><a href="'.URL_SCRIPTS.'/?script=descargar_log&log=mail_caido">Mensajes de MAILS</a></li>
                            <li><a href="saas.php?a=empresas">'.$i18n_funciones[66].'</a></li>
                        </ul></div>
                        </li>';
            }
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_clientes'] && $_SESSION['perfil_clientes_ver']) {
            echo '
                <li class="li_menu'.$li_activo['clientes'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_clientes.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['clientes'].'</span>
                        <li><a href="clientes.php?a=buscar">'.$i18n_funciones[17].'</a></li>';
            if ($_SESSION['perfil_clientes_alta'])
                echo '
                        <li><a href="clientes.php?a=alta">'.$i18n_funciones[27].'</a></li>';
            listar_informes('clientes');
            if ($_SESSION['perfil_clientes_herramientas'])
                echo '
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="clientes.php?a=exportar">'.$i18n_funciones[67].'</a></li>
                            <li><a href="clientes.php?a=importar">'.$i18n_funciones[73].'</a></li>
                        </ul></div>
                        </li>';
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_ventas'] && $_SESSION['perfil_ventas_ver']) {
            echo '
                <li class="li_menu'.$li_activo['ventas'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_ventas.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['ventas'].'</span>';
            if ($_SESSION['perfil_ventas_alta'])
                echo '
                        <li><a href="ventas.php?a=alta">'.$i18n_funciones[27].'</a></li>';
            listar_informes('ventas');
            if ($_SESSION['perfil_ventas_herramientas']) {
                echo '
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="ventas.php?a=exportar">'.$i18n_funciones[67].'</a></li>';
                if ($_SESSION['modulo_ML'] && !$_SESSION['sistema_gratis'])
                    echo '
                            <li><a href="ventas.php?a=pedidos_ml">'.$i18n_funciones[195].'</a></li>
                            <li><a href="ventas.php?a=pagos_mp">'.$i18n_funciones[196].'</a></li>';
                else if ($_SESSION['sistema_gratis'])
                    echo '
                            <li><a href="#" class="gratis" onclick="return gratis()">'.$i18n_funciones[195].'</a></li>
                            <li><a href="#" class="gratis" onclick="return gratis()">'.$i18n_funciones[196].'</a></li>';
                if ($_SESSION['configuracion_discrimina'] && !$_SESSION['sistema_gratis']) {
                    echo '
                            <li><a href="ventas.php?a=txtafip&txt=libroiva">'.$i18n_funciones[261].'</a></li>
                            <li><a href="ventas.php?a=txtafip&txt=rg3685">'.$i18n_funciones[89].'</a></li>';
                    if ($_SESSION['configuracion_emitir_impuestos'])
                        echo '
                            <li><a href="ventas.php?a=txtafip&txt=arba-percepcion">'.$i18n_funciones[300].'</a></li>';
                }
                else if ($_SESSION['configuracion_discrimina'] && $_SESSION['sistema_gratis'])
                    echo '
                            <li><a href="#" class="gratis" onclick="return gratis()">'.$i18n_funciones[261].'</a></li>
                            <li><a href="#" class="gratis" onclick="return gratis()">'.$i18n_funciones[89].'</a></li>
                            <li><a href="#" class="gratis" onclick="return gratis()">'.$i18n_funciones[300].'</a></li>';
                echo '
                        </ul></div>
                        </li>';
            }
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_cajas'] && $_SESSION['perfil_cajas_ver']) {
            echo '
                <li class="li_menu'.$li_activo['cajas'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_cajas.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['cajas'].'</span>
                        <li><a href="cajas.php?a=listar">'.$i18n_funciones[58].'</a></li>';
            listar_informes('cajas');
            if ($_SESSION['perfil_cajas_herramientas'])
                echo '
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="cajas.php?a=exportar">'.$i18n_funciones[67].'</a></li>
                            <li><a href="cheques.php?a=buscar">'.$i18n_funciones[334].'</a></li>
                        </ul></div>
                        </li>';
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_servicios'] && $_SESSION['perfil_servicios_ver']) {
            echo '
                <li class="li_menu'.$li_activo['servicios'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_servicios.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['servicios'].'</span>
                        <li><a href="servicios.php?a=listar">'.$i18n_funciones[58].'</a></li>';
            if ($_SESSION['perfil_servicios_alta'])
                echo '
                        <li><a href="servicios.php?a=alta">'.$i18n_funciones[27].'</a></li>';
            listar_informes('servicios');
            if ($_SESSION['perfil_servicios_herramientas'])
                echo '
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="servicios.php?a=exportar">'.$i18n_funciones[67].'</a></li>
                        </ul></div>
                        </li>';
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_proveedores'] && $_SESSION['perfil_proveedores_ver']) {
            echo '
                <li class="li_menu'.$li_activo['proveedores'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_proveedores.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['proveedores'].'</span>
                        <li><a href="proveedores.php?a=buscar">'.$i18n_funciones[17].'</a></li>';
            if ($_SESSION['perfil_proveedores_alta'])
                echo '
                        <li><a href="proveedores.php?a=alta">'.$i18n_funciones[27].'</a></li>';
            listar_informes('proveedores');
            if ($_SESSION['perfil_proveedores_herramientas'])
                echo '
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="proveedores.php?a=exportar">'.$i18n_funciones[67].'</a></li>
                            <li><a href="proveedores.php?a=importar">'.$i18n_funciones[73].'</a></li>
                        </ul></div>
                        </li>';
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_compras'] && $_SESSION['perfil_compras_ver']) {
            echo '
                <li class="li_menu'.$li_activo['compras'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_compras.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['compras'].'</span>';
            if ($_SESSION['perfil_compras_alta'])
                echo '
                        <li><a href="compras.php?a=alta">'.$i18n_funciones[27].'</a></li>';
            listar_informes('compras');
            if ($_SESSION['perfil_compras_herramientas']) {
                echo '
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="compras.php?a=exportar">'.$i18n_funciones[67].'</a></li>';
                if ($_SESSION['configuracion_discrimina'] && !$_SESSION['sistema_gratis']) {
                    echo '
                        <li><a href="compras.php?a=txtafip&txt=libroiva">'.$i18n_funciones[261].'</a></li>
                        <li><a href="compras.php?a=txtafip&txt=rg3685">'.$i18n_funciones[95].'</a></li>';
                    if ($_SESSION['configuracion_emitir_impuestos'])
                        echo '
                        <li><a href="compras.php?a=txtafip&txt=arba-retencion">'.$i18n_funciones[301].'</a></li>';
                } elseif ($_SESSION['configuracion_discrimina'] && $_SESSION['sistema_gratis'])
                    echo '
                        <li><a href="#" class="gratis" onclick="return gratis()">'.$i18n_funciones[261].'</a></li>
                        <li><a href="#" class="gratis" onclick="return gratis()">'.$i18n_funciones[95].'</a></li>
                        <li><a href="#" class="gratis" onclick="return gratis()">'.$i18n_funciones[301].'</a></li>';
                echo '
                        </ul></div>
                        </li>';
            }
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_productos'] && $_SESSION['perfil_productos_ver']) {
            echo '
                <li class="li_menu'.$li_activo['productos'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_productos.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['productos'].'</span>
                        <li><a href="productos.php?a=buscar">'.$i18n_funciones[17].'</a></li>';
            if ($_SESSION['perfil_productos_alta'])
                echo '
                        <li><a href="productos.php?a=alta">'.$i18n_funciones[27].'</a></li>';
            listar_informes('productos');
            if ($_SESSION['perfil_productos_herramientas'])
                echo '
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="productos.php?a=ajustar">'.$i18n_funciones[64].'</a></li>
                            <li><a href="productos.php?a=traslados">'.$i18n_funciones[171].'</a></li>
                            <li><a href="productos.php?a=exportar">'.$i18n_funciones[67].'</a>
                            <li><a href="productos.php?a=importar">'.$i18n_funciones[73].'</a></li>
                        </ul></div>
                        </li>';
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_bienes'] && $_SESSION['perfil_bienes_ver']) {
            echo '
                <li class="li_menu'.$li_activo['bienes'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_bienes.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['bienes'].'</span>
                        <li><a href="bienes.php?a=buscar">'.$i18n_funciones[17].'</a></li>';
            if ($_SESSION['perfil_bienes_alta'])
                echo '
                        <li><a href="bienes.php?a=alta">'.$i18n_funciones[27].'</a></li>';
            listar_informes('bienes');
            if ($_SESSION['perfil_bienes_herramientas'])
                echo '
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="bienes.php?a=exportar">'.$i18n_funciones[67].'</a></li>
                        </ul></div>
                        </li>';
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_conocimientos'] && $_SESSION['perfil_conocimientos_ver']) {
            echo '
                <li class="li_menu'.$li_activo['conocimientos'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_conocimientos.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['conocimientos'].'</span>
                        <li><a href="conocimientos.php?a=buscar">'.$i18n_funciones[17].'</a></li>
                        <li><a href="conocimientos.php?a=alta">'.$i18n_funciones[27].'</a></li>';
            if ($_SESSION['perfil_conocimientos_herramientas'])
                echo '
                        <li><a href="#" onclick="return false">'.$i18n_funciones[7].'</a>
                        <div class="submenu2"><ul>
                            <li><a href="conocimientos.php?a=exportar">'.$i18n_funciones[67].'</a></li>
                        </ul></div>
                        </li>';
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['modulo_configuraciones']
            && ($_SESSION['perfil_configuraciones_empresa']
                || $_SESSION['perfil_configuraciones_tablas']
                || $_SESSION['perfil_configuraciones_cuentas'])
            ) {
            echo '
                <li class="li_menu'.$li_activo['configuraciones'].'"><a href="#" onclick="return false"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_configuraciones.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['configuraciones'].'</span>';
            if ($_SESSION['perfil_configuraciones_empresa'])
                echo '
                        <li><a href="configuraciones.php?a=empresa">'.$i18n_funciones[59].'</a></li>';
            if ($_SESSION['modulo_ventas'] && $_SESSION['perfil_configuraciones_empresa'])
                echo '
                        <li><a href="configuraciones.php?a=ventas">'.$i18n_funciones['ventas'].'</a></li>';
            if ($_SESSION['modulo_compras'] && $_SESSION['perfil_configuraciones_empresa'])
                echo '
                        <li><a href="configuraciones.php?a=compras">'.$i18n_funciones['compras'].'</a></li>';
            if (($_SESSION['modulo_cajas'] && $_SESSION['perfil_configuraciones_empresa']) || $_SESSION['sistema_gratis'])
                echo '
                        <li><a '.(!$_SESSION['sistema_gratis']
                            ? 'href="configuraciones.php?a=cajas">'
                            : 'href="#" onclick="gratis()" class="gratis">')
                            .$i18n_funciones['cajas'].'</a></li>';
            if ($_SESSION['perfil_configuraciones_empresa'])
                echo '
                        <li><a '.(!$_SESSION['sistema_gratis']
                            ? 'href="configuraciones.php?a=perfiles">'
                            : 'href="#" onclick="gratis()" class="gratis">')
                            .$i18n_funciones[60].'</a></li>';
            if ($_SESSION['perfil_configuraciones_cuentas'])
                echo '
                        <li><a '.(!$_SESSION['sistema_gratis']
                            ? 'href="configuraciones.php?a=usuarios">'
                            : 'href="#" onclick="gratis()" class="gratis">')
                            .$i18n_funciones[61].'</a></li>';
            if ($_SESSION['modulo_ventas'] && $_SESSION['perfil_configuraciones_empresa'])
                echo '
                        <li><a href="configuraciones.php?a=wsfe">'.$i18n_funciones[82].'</a></li>';
            if (($_SESSION['modulo_tienda'] && $_SESSION['perfil_configuraciones_empresa']) || $_SESSION['sistema_gratis'])
                echo '
                        <li><a '.(!$_SESSION['sistema_gratis']
                            ? 'href="configuraciones.php?a=tienda">'
                            : 'href="#" onclick="gratis()" class="gratis">')
                            .$i18n_funciones[79].'</a></li>';
            if (($_SESSION['modulo_ML'] && $_SESSION['perfil_configuraciones_empresa']) || $_SESSION['sistema_gratis'])
                echo '
                        <li><a '.(!$_SESSION['sistema_gratis']
                            ? 'href="configuraciones.php?a=ml">'
                            : 'href="#" onclick="gratis()" class="gratis">')
                            .$i18n_funciones[81].'</a></li>';
            if ($_SESSION['perfil_configuraciones_empresa']
                && contar_sql(consulta_sql("SELECT idscript FROM scripts LIMIT 1"))) {
                echo '
                        <li><a href="configuraciones.php?a=scripts">'.$i18n_funciones['scripts'].'</a></li>';
            }
            if ($_SESSION['perfil_configuraciones_tablas'] || $_SESSION['sistema_gratis']) {
                echo '
                        <li><a '.(!$_SESSION['sistema_gratis']
                            ? 'href="configuraciones.php?a=plantillas">'
                            : 'href="#" onclick="gratis()" class="gratis">')
                            .$i18n_funciones[90].'</a></li>
                        <li><a '.(!$_SESSION['sistema_gratis']
                            ? 'href="configuraciones.php?a=categorias" title="'.$i18n_funciones[69].'">'
                            : 'href="#" onclick="gratis()" class="gratis">')
                            .$i18n_funciones[65].'</a></li>';
            }
            echo '
                    </ul></div>
                </li>';
        }

        if ($_SESSION['sistema_gratis']) {
            echo '
                <li></li>
                <li class="li_menu gratis"><a href="#" onclick="gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_cajas.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['cajas'].'</span>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[58].'</a></li>';
            listar_informes('cajas');
            echo '
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[7].'</a></li>
                    </ul></div>
                </li>
                <li class="li_menu gratis"><a href="#" onclick="gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_servicios.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['servicios'].'</span>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[58].'</a></li>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[27].'</a></li>';
            listar_informes('servicios');
            echo '
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[7].'</a></li>
                    </ul></div>
                </li>
                <li class="li_menu gratis"><a href="#" onclick="gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_proveedores.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['proveedores'].'</span>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[17].'</a></li>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[27].'</a></li>';
            listar_informes('proveedores');
            echo '
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[7].'</a></li>
                    </ul></div>
                </li>
                <li class="li_menu gratis"><a href="#" onclick="gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_compras.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['compras'].'</span>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[27].'</a></li>';
            listar_informes('compras');
            echo '
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[7].'</a></li>
                    </ul></div>
                </li>
                <li class="li_menu gratis"><a href="#" onclick="gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_productos.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['productos'].'</span>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[17].'</a></li>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[27].'</a></li>';
            listar_informes('productos');
            echo '
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[7].'</a></li>
                    </ul></div>
                </li>
                <li class="li_menu gratis"><a href="#" onclick="gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_bienes.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['bienes'].'</span>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[17].'</a></li>';
            listar_informes('bienes');
            echo '
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[7].'</a></li>
                    </ul></div>
                </li>
                <li class="li_menu gratis"><a href="#" onclick="gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_conocimientos.png" style="cursor: pointer;"></a>
                    <div class="submenu"><ul><span class="menu_titulo">'.$i18n_funciones['conocimientos'].'</span>
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[17].'</a></li>';
            listar_informes('conocimientos');
            echo '
                        <li><a href="#" onclick="gratis()">'.$i18n_funciones[7].'</a></li>
                    </ul></div>
                </li>';
        }

        echo '</ul>
        </div>
    </div>';

    } else {

        echo '
        <div class="mobile-container">
            <div id="flotante"></div>
            <div class="topnav">
                <div class="saas-horizontal-menu">
                    <a href="/" class="active"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/saas-horizontal-menu.png"></a>
                </div>
                <div id="menu" style="display: none;">
                        <a href="/" class="active"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_inicio.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-inicio">Inicio</a>
                    <!-- Empresa -->
        ';

        if (SAAS_ID == $_SESSION['empresa_idempresa'] && $_SESSION['perfil_saas_ver']) {
            echo '
                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-saas`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_saas.png" class="icono-saas">'.$i18n_funciones['saas'].'</a>
                    <div id="menu-saas" class="submenu" style="display: none;">
            ';
                if ($_SESSION['perfil_saas_empresas']) {
                    echo '
                        <a href="/saas.php?a=buscar">'.$i18n_funciones[17].'</a>
                    ';
                }

                if ($_SESSION['perfil_saas_tickets']) {
                    echo '
                        <a href="/saas.php?a=tickets">'.$i18n_funciones[62].'</a>
                        <a href="/saas.php?a=faqs">'.$i18n_funciones[170].'</a>
                    ';
                }

                if ($_SESSION['perfil_saas_informes']) {
                    echo '
                        <a href="/saas.php?a=mailing">'.$i18n_funciones[166].'</a>
                        <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-saas-estadisticas`)">'.$i18n_funciones[94].'</a>
                        <div id="menu-saas-estadisticas" class="submenu2" style="display: none;">
                            <a href="'.URL_SCRIPTS.'/estadisticas/estatico.php">'.$i18n_funciones['249'].'</a>
                            <a href="'.URL_SCRIPTS.'/estadisticas/feg.php">'.$i18n_funciones['250'].'</a>
                            <a href="'.URL_SCRIPTS.'/estadisticas/uso.php">'.$i18n_funciones['251'].'</a>
                        </div>
                        <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-saas-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-saas-herramientas" class="submenu2" style="display: none;">
                            <a href="'.URL_SCRIPTS.'/?script=descargar_log&log=afip_caido">'.$i18n_funciones[252].'</a>
                            <a href="'.URL_SCRIPTS.'/?script=descargar_log&log=mail_caido">'.$i18n_funciones[253].'</a>
                            <a href="/saas.php?a=empresas">'.$i18n_funciones[66].'</a>
                        </div>
                    ';
                }
                echo '
                    </div>
            ';
        }

        if ($_SESSION['modulo_clientes'] && $_SESSION['perfil_clientes_ver']) {
            echo '
                    <!-- Clientes -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-clientes`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_clientes.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-clientes">'.$i18n_funciones['clientes'].'</a>
                    <div id="menu-clientes" class="submenu" style="display: none;">
                        <a href="/clientes.php?a=buscar">'.$i18n_funciones[17].'</a>';
            if ($_SESSION['perfil_clientes_alta']) {
                echo    '<a href="/clientes.php?a=alta">'.$i18n_funciones[27].'</a>';
            }
            if ($_SESSION['modulo_informes'] && $_SESSION['perfil_clientes_informes']) {
                echo '  <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-clientes-informes`)">'.$i18n_funciones['informes'].'</a>
                        <div id="menu-clientes-informes" class="submenu2" style="display: none;">
                            <a href="/informes.php?a=new&id=1">'.$i18n_funciones['218'].'</a>
                            <a href="/informes.php?a=new&id=28">'.$i18n_funciones['219'].'</a>
                            <a href="'.URL_INFORMES.'/informes/mod/7?id=7&a=mod">'.$i18n_funciones['220'].'</a>
                        </div>';
            }
            if ($_SESSION['perfil_clientes_herramientas']) {
                echo '  <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-clientes-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-clientes-herramientas" class="submenu2" style="display: none;">
                            <a href="/clientes.php?a=exportar">'.$i18n_funciones[67].'</a>
                            <a href="/clientes.php?a=importar">'.$i18n_funciones[73].'</a>
                        </div>';
            }
                echo '
                    </div>';
        }

        if ($_SESSION['modulo_ventas'] && $_SESSION['perfil_ventas_ver']) {
            echo '
                    <!-- Ventas -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-ventas`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_ventas.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-ventas">'.$i18n_funciones['ventas'].'</a>
                    <div id="menu-ventas" class="submenu" style="display: none;">';
            if ($_SESSION['perfil_ventas_alta']) {
                echo    '<a href="/ventas.php?a=alta">'.$i18n_funciones[27].'</a>';
            }
            if ($_SESSION['modulo_informes'] && $_SESSION['perfil_ventas_informes']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-ventas-informes`)">'.$i18n_funciones['informes'].'</a>
                        <div id="menu-ventas-informes" class="submenu2" style="display: none;">
                            <a href="/informes.php?a=new&id=14">'.$i18n_funciones[221].'</a>
                            <a href="/informes.php?a=new&id=32">'.$i18n_funciones[222].'</a>
                            <a href="/informes.php?a=new&id=31">'.$i18n_funciones[306].'</a>
                            <a href="/informes.php?a=new&id=15">'.$i18n_funciones[224].'</a>
                            <a href="/informes.php?a=new&id=16">'.$i18n_funciones[225].'</a>
                            <a href="/informes.php?a=new&id=38">'.$i18n_funciones[226].'</a>
                            <a href="/informes.php?a=new&id=17">'.$i18n_funciones[227].'</a>
                        </div>';
            }
            if ($_SESSION['perfil_ventas_herramientas']) {
                echo '  <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-ventas-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-ventas-herramientas" class="submenu2" style="display: none;">
                            <a href="/ventas.php?a=exportar">'.$i18n_funciones[67].'</a>';
                if ($_SESSION['modulo_ML']) {
                    echo '  <a href="/ventas.php?a=pedidos_ml">'.$i18n_funciones[195].'</a>
                            <a href="/ventas.php?a=pagos_mp">'.$i18n_funciones[196].'</a>';
                }
                if ($_SESSION['configuracion_discrimina'] && !$_SESSION['sistema_gratis']) {
                    echo '  <a href="/ventas.php?a=txtafip&txt=libroiva">'.$i18n_funciones[261].'</a>
                            <a href="/ventas.php?a=txtafip&txt=rg3685">'.$i18n_funciones[89].'</a>';
                    if ($_SESSION['configuracion_emitir_impuestos'])
                        echo '
                            <a href="ventas.php?a=txtafip&txt=arba-percepcion">'.$i18n_funciones[300].'</a>';
                }
                    echo '
                        </div>';
            }
                echo '
                    </div>';
        }

        if ($_SESSION['modulo_cajas'] && $_SESSION['perfil_cajas_ver']) {
            echo'
                    <!-- Cajas -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-cajas`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_cajas.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-cajas">'.$i18n_funciones['cajas'].'</a>
                    <div id="menu-cajas" class="submenu" style="display: none;">
                        <a href="/cajas.php?a=listar">'.$i18n_funciones[58].'</a>';
            if ($_SESSION['modulo_informes'] && $_SESSION['perfil_cajas_informes']) {
                echo '  <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-cajas-informes`)">'.$i18n_funciones['informes'].'</a>
                        <div id="menu-cajas-informes" class="submenu2" style="display: none;">
                            <a href="/informes.php?a=new&id=23">'.$i18n_funciones[231].'</a>
                            <a href="/informes.php?a=new&id=24">'.$i18n_funciones[232].'</a>
                            <a href="/informes.php?a=new&id=25">'.$i18n_funciones[233].'</a>
                            <a href="'.URL_INFORMES.'/informes/mod/39?id=39&a=mod">'.$i18n_funciones['309'].'</a>
                        </div>';
            }
            if ($_SESSION['perfil_cajas_herramientas']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-cajas-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-cajas-herramientas" class="submenu2" style="display: none;">
                            <a href="/cajas.php?a=exportar">'.$i18n_funciones[67].'</a>
                        </div>';
            }
                echo '
                    </div>';
        }

        if ($_SESSION['modulo_servicios'] && $_SESSION['perfil_servicios_ver']) {
            echo '
                    <!-- Servicios -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-servicios`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_servicios.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-servicios">'.$i18n_funciones['servicios'].'</a>
                    <div id="menu-servicios" class="submenu" style="display: none;">
                        <a href="/servicios.php?a=listar">'.$i18n_funciones[58].'</a>';
            if ($_SESSION['perfil_servicios_alta']) {
                echo    '<a href="/servicios.php?a=alta">'.$i18n_funciones[27].'</a>';
            }
            if ($_SESSION['modulo_informes'] && $_SESSION['perfil_servicios_informes']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-servicios-informes`)">'.$i18n_funciones['informes'].'</a>
                        <div id="menu-servicios-informes" class="submenu2" style="display: none;">
                            <a href="/informes.php?a=new&id=3">'.$i18n_funciones[234].'</a>
                            <a href="'.URL_INFORMES.'/informes/mod/4?id=4&a=mod">'.$i18n_funciones[235].'</a>
                            <a href="'.URL_INFORMES.'/informes/mod/5?id=5&a=mod">'.$i18n_funciones[236].'</a>
                            <a href="/informes.php?a=new&id=29">'.$i18n_funciones[237].'</a>
                        </div>';
            }
            if ($_SESSION['perfil_servicios_herramientas']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-servicios-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-servicios-herramientas" class="submenu2" style="display: none;">
                            <a href="/servicios.php?a=exportar">'.$i18n_funciones[67].'</a>
                        </div>';
            }
                echo'
                    </div>';
        }

        if ($_SESSION['modulo_proveedores'] && $_SESSION['perfil_proveedores_ver']) {
            echo '

                    <!-- Proveedores -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-proveedores`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_proveedores.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-proveedores">'.$i18n_funciones['proveedores'].'</a>
                    <div id="menu-proveedores" class="submenu" style="display: none;">
                        <a href="/proveedores.php?a=buscar">'.$i18n_funciones[17].'</a>';
            if ($_SESSION['perfil_proveedores_alta']) {
                echo    '<a href="/proveedores.php?a=alta">'.$i18n_funciones[27].'</a>';
            }
            if ($_SESSION['modulo_informes'] && $_SESSION['perfil_proveedores_informes']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-proveedores-informes`)">'.$i18n_funciones['informes'].'</a>
                        <div id="menu-proveedores-informes" class="submenu2" style="display: none;">
                            <a href="/informes.php?a=new&id=34">'.$i18n_funciones['238'].'</a>
                            <a href="'.URL_INFORMES.'/informes/mod/18?id=18&a=mod">'.$i18n_funciones['239'].'</a>
                        </div>';
            }
            if ($_SESSION['perfil_proveedores_herramientas']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-proveedores-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-proveedores-herramientas" class="submenu2" style="display: none;">
                            <a href="/proveedores.php?a=exportar">'.$i18n_funciones[67].'</a>
                            <a href="/proveedores.php?a=importar">'.$i18n_funciones[73].'</a>
                        </div>';
                }
                echo '
                    </div>';
        }

        if ($_SESSION['modulo_compras'] && $_SESSION['perfil_compras_ver']) {
            echo '
                    <!-- Compras -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-compras`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_compras.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-compras">'.$i18n_funciones['compras'].'</a>
                    <div id="menu-compras" class="submenu" style="display: none;">';
            if ($_SESSION['perfil_compras_alta']) {
                echo    '<a href="/compras.php?a=alta">'.$i18n_funciones[27].'</a>';
            }
            if ($_SESSION['modulo_informes'] && $_SESSION['perfil_compras_informes']) {
                echo '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-compras-informes`)">'.$i18n_funciones['informes'].'</a>
                        <div id="menu-compras-informes" class="submenu2" style="display: none;">
                            <a href="/informes.php?a=new&id=19">'.$i18n_funciones['240'].'</a>
                            <a href="/informes.php?a=new&id=33">'.$i18n_funciones['241'].'</a>
                            <a href="/informes.php?a=new&id=20">'.$i18n_funciones['242'].'</a>
                            <a href="/informes.php?a=new&id=21">'.$i18n_funciones['243'].'</a>
                            <a href="/informes.php?a=new&id=22">'.$i18n_funciones['244'].'</a>
                        </div>';
            }
            if ($_SESSION['perfil_compras_herramientas']) {
                echo '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-compras-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-compras-herramientas" class="submenu2" style="display: none;">
                            <a href="/compras.php?a=exportar">'.$i18n_funciones[67].'</a>';

                if ($_SESSION['configuracion_discrimina'] && !$_SESSION['sistema_gratis']) {
                    echo '<a href="/compras.php?a=txtafip&txt=libroiva">'.$i18n_funciones[261].'</a>
                          <a href="/compras.php?a=txtafip&txt=rg3685">'.$i18n_funciones[95].'</a>';
                    if ($_SESSION['configuracion_emitir_impuestos'])
                        echo '
                            <a href="/compras.php?a=txtafip&txt=arba-retencion">'.$i18n_funciones[301].'</a>';

                }
                    echo '
                        </div>';
            }
            echo '
                    </div>';
        }

        if ($_SESSION['modulo_productos'] && $_SESSION['perfil_productos_ver']) {
            echo '
                    <!-- Productos -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-productos`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_productos.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-productos">'.$i18n_funciones['productos'].'</a>
                    <div id="menu-productos" class="submenu" style="display: none;">
                        <a href="/productos.php?a=buscar">'.$i18n_funciones[17].'</a>';
            if ($_SESSION['perfil_productos_alta']) {
                echo    '<a href="/productos.php?a=alta">'.$i18n_funciones[27].'</a>';
            }
            if ($_SESSION['modulo_informes'] && $_SESSION['perfil_productos_informes']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-productos-informes`)">'.$i18n_funciones['informes'].'</a>
                        <div id="menu-productos-informes" class="submenu2" style="display: none;">
                            <a href="/informes.php?a=new&id=37">'.$i18n_funciones['246'].'</a>
                            <a href="'.URL_INFORMES.'/informes/mod/37?id=37&a=mod?habilitacion=habilitado&mostrar_stocktotal=1&mostrar_stockvalorizado=1&mostrar_unidad=1&mostrar_totales=1&">'.$i18n_funciones['247'].'</a>
                            <a href="'.URL_INFORMES.'/informes/mod/37?id=37&a=mod?habilitacion=habilitado&mostrar_stocktotal=1&stock=debajo&mostrar_costo=1&mostrar_stockactual=1&mostrar_stockideal=1&mostrar_stockminimo=1&mostrar_stockvalorizado=1&mostrar_proveedor=1&mostrar_rubro=1&mostrar_codigoproveedor=1&mostrar_unidad=1&mostrar_faltantes=1&solo_faltantes=1&">'.$i18n_funciones['248'].'</a>
                        </div>';
            }
            if ($_SESSION['perfil_productos_herramientas']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-productos-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-productos-herramientas" class="submenu2" style="display: none;">
                            <a href="/productos.php?a=ajustar">'.$i18n_funciones[64].'</a>
                            <a href="/productos.php?a=traslados">'.$i18n_funciones[171].'</a>
                            <a href="/productos.php?a=exportar">'.$i18n_funciones[67].'</a>
                            <a href="/productos.php?a=importar">'.$i18n_funciones[73].'</a>
                        </div>';
            }
            echo '
                    </div>';
        }

        if ($_SESSION['modulo_bienes'] && $_SESSION['perfil_bienes_ver']) {
            echo'
                    <!-- Bienes -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-bienes`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_bienes.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-bienes">'.$i18n_funciones['bienes'].'</a>
                    <div id="menu-bienes" class="submenu" style="display: none;">
                        <a href="/bienes.php?a=buscar">'.$i18n_funciones[17].'</a>';
            if ($_SESSION['perfil_bienes_alta']) {
                echo    '<a href="/bienes.php?a=alta">'.$i18n_funciones[27].'</a>';
            }
            if ($_SESSION['perfil_bienes_herramientas']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-bienes-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-bienes-herramientas" class="submenu2" style="display: none;">
                            <a href="/bienes.php?a=exportar">'.$i18n_funciones[67].'</a>
                        </div>';
            }
                echo '
                    </div>';
        }

        if ($_SESSION['modulo_conocimientos'] && $_SESSION['perfil_conocimientos_ver']) {
            echo '

                    <!-- Notas -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-conocimientos`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_conocimientos.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-conocimientos">'.$i18n_funciones['conocimientos'].'</a>
                    <div id="menu-conocimientos" class="submenu" style="display: none;">
                        <a href="/conocimientos.php?a=buscar">'.$i18n_funciones[17].'</a>
                        <a href="/conocimientos.php?a=alta">'.$i18n_funciones[27].'</a>';
            if ($_SESSION['perfil_conocimientos_herramientas']) {
                echo    '<a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-conocimientos-herramientas`)">'.$i18n_funciones[7].'</a>
                        <div id="menu-conocimientos-herramientas" class="submenu2" style="display: none;">
                            <a href="/conocimientos.php?a=exportar">'.$i18n_funciones[67].'</a>
                        </div>';
            }
                echo '
                    </div>';
        }

        if ($_SESSION['modulo_configuraciones'] && ($_SESSION['perfil_configuraciones_empresa']
            || $_SESSION['perfil_configuraciones_tablas'] || $_SESSION['perfil_configuraciones_cuentas'])) {
            echo '
                    <!-- Configuraciones -->

                    <a href="#" onclick="event.preventDefault(); abrirSubMenu(`menu-configuraciones`)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_configuraciones.png" title="'.$i18n_funciones[1].'"  class="icono-png icono-configuraciones">'.$i18n_funciones['configuraciones'].'</a>
                    <div id="menu-configuraciones" class="submenu" style="display: none;">';
            if ($_SESSION['perfil_configuraciones_empresa']) {
                echo    '<a href="/configuraciones.php?a=empresa">'.$i18n_funciones[59].'</a>';
            }
            if ($_SESSION['modulo_ventas'] && $_SESSION['perfil_configuraciones_empresa']) {
                echo '  <a href="/configuraciones.php?a=ventas">'.$i18n_funciones['ventas'].'</a>';
            }
            if ($_SESSION['modulo_compras'] && $_SESSION['perfil_configuraciones_empresa']) {
                echo '  <a href="/configuraciones.php?a=compras">'.$i18n_funciones['compras'].'</a>';
            }
            if (($_SESSION['modulo_cajas'] && $_SESSION['perfil_configuraciones_empresa']) || $_SESSION['sistema_gratis']) {
                echo '  <a href="/configuraciones.php?a=cajas">'.$i18n_funciones['cajas'].'</a>';
            }
            if ($_SESSION['perfil_configuraciones_empresa']) {
                echo '  <a href="/configuraciones.php?a=perfiles">'.$i18n_funciones[60].'</a>';
            }
            if ($_SESSION['perfil_configuraciones_cuentas']) {
                echo '  <a href="/configuraciones.php?a=usuarios">'.$i18n_funciones[61].'</a>';
            }
            if ($_SESSION['modulo_ventas'] && $_SESSION['perfil_configuraciones_empresa']) {
                echo '  <a href="/configuraciones.php?a=wsfe">'.$i18n_funciones[82].'</a>';
            }
            if (($_SESSION['modulo_tienda'] && $_SESSION['perfil_configuraciones_empresa']) || $_SESSION['sistema_gratis']) {
                echo '  <a href="/configuraciones.php?a=tienda">'.$i18n_funciones[79].'</a>';
            }
            if (($_SESSION['modulo_ML'] && $_SESSION['perfil_configuraciones_empresa']) || $_SESSION['sistema_gratis']) {
                echo '  <a href="/configuraciones.php?a=ml">'.$i18n_funciones[81].'</a>';
            }
            if ($_SESSION['perfil_configuraciones_empresa'] && contar_sql(consulta_sql("SELECT idscript FROM scripts LIMIT 1"))) {
                echo '  <a href="/configuraciones.php?a=scripts">'.$i18n_funciones['scripts'].'</a>';
            }
            if ($_SESSION['perfil_configuraciones_tablas'] || $_SESSION['sistema_gratis']) {
                echo '  <a href="/configuraciones.php?a=plantillas">'.$i18n_funciones[90].'</a>
                        <a href="/configuraciones.php?a=categorias">'.$i18n_funciones[65].'</a>';
            }

            echo '
                    </div>

                    <div id="botones-encabezado-menu" class="botones-menu">';

                    echo '
                    <a href="ayudas.php"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/indice.png" title="'.$i18n_funciones[1].'" id="resaltar"></a>';

                    $notificaciones_sin_leer = campo_sql(consulta_sql("SELECT COUNT(*) FROM mensajes WHERE tipo IN ('Notificacion', 'Consulta', 'Informacion') AND idusuario = '{$_SESSION['usuario_idusuario']}' AND visto = 0"));
                    if ($notificaciones_sin_leer)
                        echo '
                        <a id="icono_notificaciones" href="javascript:void(0)" onclick="modal(\'mensajes\', \'mensajes_notificaciones\', \'\', \'\'); return false;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/notificaciones_alerta.png" title="'.str_replace('{notificaciones_sin_leer}', $notificaciones_sin_leer, $i18n_funciones[305]).'"></a>';
                    else
                        echo '
                        <a id="icono_notificaciones" href="javascript:void(0)" onclick="modal(\'mensajes\', \'mensajes_notificaciones\', \'\', \'\'); return false;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/notificaciones.png" title="'.$i18n_funciones[304].'"></a>';

                        if ($_SESSION['sistema_gratis']) {
                            if ($_SESSION['sistema_full_probado'])
                                echo '
                                <a href="#" class="ad_feg_encabezado" onclick="event.preventDefault(); gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/full_encabezado.png"></a>';
                            else
                                echo '
                                <a href="#" class="ad_feg_encabezado" onclick="event.preventDefault(); gratis()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/feg_encabezado.jpg"></a>';
                        }
                        if ($_SESSION['perfil_usuarios_mod'])
                            echo '
                            <a href="usuarios.php?a=mod&id='.$_SESSION['usuario_idusuario'].'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/config.png" title="'.$i18n_funciones[1].'" class="icono-png icono-configuraciones"></a>';
                        if ($_SESSION['perfil_configuraciones_empresa'] || $_SESSION['perfil_configuraciones_tablas']) {
                            // La empresa tiene saldo y ya se le venció la factura
                            if (date("j") > 10
                                && $_SESSION['saldo']
                                && !$_SESSION['sistema_gratis']
                                && $_SESSION['empresa_estado'] == 'activada') {
                                echo '
                            <a href="configuraciones.php?a=situacion"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/empresa_alerta.png" title="'.$i18n_funciones[93].$i18n_funciones[105].'"></a>';

                            } else {
                                echo '
                            <a href="configuraciones.php?a=situacion"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/empresa.png" title="'.$i18n_funciones[1].'" class="icono-png icono-empresa"></a>';
                            }
                        }
                        echo '
                            <a href="#" onclick="event.preventDefault(); salir()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/salir.png" title="'.$i18n_funciones[1].'" class="icono-png icono-salir"></a>
                    </div>
                    <div id="encabezado-empresa-menu-fin">
                        <p class="encabezado_empresa">'.$_SESSION['empresa_nombre'].' <small>(Id '.$_SESSION['empresa_idempresa'].')</small></p>
                        <p class="encabezado_usuario">'.$_SESSION['usuario_nombre'].' ('.$_SESSION['usuario_mail'].')</p>
                        <p class="encabezado_version">'.$_SESSION['sistema_nombre'].' '.$_SESSION['version'].' '.$_SESSION['servidor_version'].'</p>

                    </div>';
        }

            echo'

                </div>

                <a class="icon" href="#" onclick="event.preventDefault(); abrirMenu()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/menu.png"></a>
            </div>
        </div>';
    }
}

// Utilizado para poner la clase de nuevo temporalmente a las nuevas funcionalidades
function nuevos($modal = false) {
    global $modulo;
    global $a;
    global $ventana;

    $nuevos = json_decode(file_get_contents(__DIR__.'/../nuevos.json'));

    foreach (array('nuevo-bg', 'nuevo-boton', 'nuevo-menu') as $tipo_nuevo) {
        $selectores = array();
        foreach ($nuevos->{$tipo_nuevo} as $nuevo) {
            if ($modal) {
                if ($ventana == $nuevo->ventana)
                    $selectores[] = '.modal '.$nuevo->selector;
            } else {
                if (($nuevo->modulo == $modulo || $nuevo->modulo == '*')
                    && ($nuevo->a == $a || $nuevo->a == '*'))
                    $selectores[] = $nuevo->selector;
            }
        }
        if (count($selectores)) {
            echo '
            <script type="text/javascript" charset="utf-8">
                $(function() {
                    $("'.implode(', ',$selectores).'").addClass("'.$tipo_nuevo.'");
                });
            </script>';
        }
    }

}

function detect_responsive() {
    global $i18n_funciones;

    $detect_responsive = preg_match("/(android|avantgo|blackberry|bolt|boost|cricket|docomo|fone|hiptop|mini|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i", $_SERVER["HTTP_USER_AGENT"]);

    if ($detect_responsive && $_SESSION['usuario_idestilo'] == '1') {
        consulta_sql("UPDATE usuarios SET idestilo = 2
            WHERE idusuario = '".$_SESSION['usuario_idusuario']."'");
        $_SESSION['usuario_idestilo'] = 2;
        mensajes_alta($i18n_funciones[303], 'Confirmacion');
        mensajes_alta(str_replace('{idusuario}', $_SESSION['usuario_idusuario'], $i18n_funciones[308]), 'Notificacion');
    }

    return $detect_responsive;
}
